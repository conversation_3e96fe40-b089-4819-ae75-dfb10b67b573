import { Match, Standing } from '@/types/simulation';

// Group fixtures by week
export function groupFixturesByWeek(fixtures: Match[]) {
    return fixtures.reduce((acc, match) => {
        if (!acc[match.week]) {
            acc[match.week] = [];
        }
        acc[match.week].push(match);
        return acc;
    }, {} as Record<number, Match[]>);
}

export function calculateStandings(fixtures: Match[]): Standing[] {
    const teamStats: Record<string, Standing> = {};

    // Initialize all teams
    fixtures.forEach(match => {
        if (!teamStats[match.home_team.name]) {
            teamStats[match.home_team.name] = {
                team: match.home_team.name,
                played: 0,
                won: 0,
                drawn: 0,
                lost: 0,
                points: 0
            };
        }
        if (!teamStats[match.away_team.name]) {
            teamStats[match.away_team.name] = {
                team: match.away_team.name,
                played: 0,
                won: 0,
                drawn: 0,
                lost: 0,
                points: 0
            };
        }
    });

    // Calculate stats from played matches
    fixtures.filter(match => match.is_played).forEach(match => {
        const homeTeam = teamStats[match.home_team.name];
        const awayTeam = teamStats[match.away_team.name];

        homeTeam.played++;
        awayTeam.played++;

        if ((match.home_score || 0) > (match.away_score || 0)) {
            homeTeam.won++;
            homeTeam.points += 3;
            awayTeam.lost++;
        } else if ((match.home_score || 0) < (match.away_score || 0)) {
            awayTeam.won++;
            awayTeam.points += 3;
            homeTeam.lost++;
        } else {
            homeTeam.drawn++;
            awayTeam.drawn++;
            homeTeam.points += 1;
            awayTeam.points += 1;
        }
    });

    return Object.values(teamStats).sort((a, b) => b.points - a.points);
}

// Calculate championship predictions
export function calculatePredictions(standings: Standing[]): Record<string, number> {
    const totalPoints = standings.reduce((sum, team) => sum + team.points, 0);
    const predictions: Record<string, number> = {};

    if (totalPoints === 0) {
        // Equal chances at start
        standings.forEach(team => {
            predictions[team.team] = Math.round(100 / standings.length);
        });
    } else {
        standings.forEach((team, index) => {
            // Leader gets higher chance, others get proportional
            const baseChance = totalPoints > 0 ? (team.points / totalPoints) * 100 : 25;
            const positionBonus = (standings.length - index) * 5;
            predictions[team.team] = Math.min(Math.round(baseChance + positionBonus), 100);
        });
    }

    return predictions;
}

export function isSeasonComplete(fixtures: Match[]): boolean {
    const maxWeek = Math.max(...fixtures.map(f => f.week));
    return maxWeek >= 6 && fixtures.filter(f => f.week === maxWeek).every(f => f.is_played);
}

export function getCurrentWeek(fixtures: Match[]): number | null {
    const unplayedWeeks = fixtures
        .filter(f => !f.is_played)
        .map(f => f.week)
        .sort((a, b) => a - b);

    return unplayedWeeks.length > 0 ? unplayedWeeks[0] : null;
}

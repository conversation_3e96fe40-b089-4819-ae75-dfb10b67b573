import { Head, router } from '@inertiajs/react';
import { useMemo, useState, useEffect } from 'react';
import { RotateCcw } from 'lucide-react';
import { SimulationProps } from '@/types/simulation';
import {
    groupFixturesByWeek,
    calculateStandings,
    calculatePredictions,
    isSeasonComplete,
    getCurrentWeek
} from '@/utils/simulationUtils';
import StandingsColumn from '@/components/StandingsColumn';
import FixturesColumn from '@/components/FixturesColumn';
import PredictionsColumn from '@/components/PredictionsColumn';

export default function Simulation({ fixtures, seasonId }: SimulationProps) {
    // State to manage current fixtures
    const [currentFixtures, setCurrentFixtures] = useState(fixtures);
    const [currentSeasonId] = useState(seasonId);

    useEffect(() => {
        // Save seasonId to cookie when it changes
        document.cookie = `simulation_season_id=${currentSeasonId}; path=/; max-age=${7 * 24 * 60 * 60}`; // 7 days
    }, [currentSeasonId]);

    // Handle both array and object with data property
    const fixturesArray = Array.isArray(currentFixtures) ? currentFixtures : currentFixtures.data || [];

    // Group fixtures by week using utility function
    const fixturesByWeek = useMemo(() => groupFixturesByWeek(fixturesArray), [fixturesArray]);
    const weekNumbers = Object.keys(fixturesByWeek).sort((a, b) => parseInt(a) - parseInt(b));

    // Calculate standings using utility function
    const standings = useMemo(() => calculateStandings(fixturesArray), [fixturesArray]);

    // Calculate championship predictions using utility function
    const predictions = useMemo(() => calculatePredictions(standings), [standings]);

    // Check if season is complete or if we're at week 6
    const seasonComplete = useMemo(() => isSeasonComplete(fixturesArray), [fixturesArray]);
    const currentWeek = useMemo(() => getCurrentWeek(fixturesArray), [fixturesArray]);
    const buttonsDisabled = seasonComplete || (currentWeek !== null && currentWeek > 6);

    const handleFixturesUpdate = (fixtures: any) => {
        console.log('Fixtures updated:', fixtures);
        setCurrentFixtures(fixtures);
    };


    const handleReset = () => {
        router.visit('/', { method: 'get' });
    };

    return (
        <>
            <Head title="League Simulation" />
            <div className="min-h-screen bg-gray-100 py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="mb-6">
                        <h1 className="text-3xl font-bold text-gray-900">League Simulation</h1>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <StandingsColumn standings={standings} />

                        <FixturesColumn
                            fixturesByWeek={fixturesByWeek}
                            weekNumbers={weekNumbers}
                            buttonsDisabled={buttonsDisabled}
                            seasonId={currentSeasonId.toString()}
                            onFixturesUpdate={handleFixturesUpdate}
                        />

                        <PredictionsColumn predictions={predictions} />
                    </div>

                    {/* Reset Button - Bottom Right */}
                    <div className="fixed bottom-6 right-6">
                        <button
                            onClick={handleReset}
                            className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg shadow-lg hover:bg-red-700 transition-colors"
                        >
                            <RotateCcw size={16} />
                            <span>Reset</span>
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
}
